name: Trigger Terraform Deploy

on:
  push:
    branches:
      - develop      # Adjust branch as needed
      # - node-upgrade # Add additional branches as needed
      # - main         # For production deployments

jobs:
  trigger-terraform:
    runs-on: ubuntu-latest
    
    steps:
    - name: Determine environment
      id: env
      run: |
        if [ "${{ github.ref }}" == "refs/heads/main" ]; then
          echo "environment=production" >> $GITHUB_OUTPUT
        else
          echo "environment=sandbox" >> $GITHUB_OUTPUT
        fi
        
    - name: Trigger terraform deployment
      uses: peter-evans/repository-dispatch@v3
      with:
        token: ${{ secrets.TERRAFORM_DEPLOY_TOKEN }}
        repository: 20DegreesFit/20deg-terraform
        event-type: lambda-deploy
        client-payload: |
          {
            "service": "lambda-target_achievement",
            "repo_name": "${{ github.event.repository.name }}",
            "branch": "${{ github.ref_name }}",
            "commit_sha": "${{ github.sha }}",
            "environment": "${{ steps.env.outputs.environment }}"
          }