const lambdaLocal = require("lambda-local");
const handler = require("../index"); // Adjust this line if needed
const bodyJson = require("./testcase.json");
const event = {
  Records: [{ body: JSON.stringify(bodyJson) }],
};
const TIMEOUT_MS = 60*1000* 100;
lambdaLocal.execute({
  event,
  lambdaFunc: handler,
  lambdaHandler: "handler", // Ensure this matches the exported function name
  timeoutMs: TIMEOUT_MS,
  callback: (err, data) => {
    if (err) {
      console.error(err);
    } else {
      console.log(data);
      return data;
    }
  },
});
