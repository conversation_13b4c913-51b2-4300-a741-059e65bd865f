const AWS = require("aws-sdk");
AWS.config.update({ region: "us-east-1" });
const lambda = new AWS.Lambda({ apiVersion: "2015-03-31" });
const { config } = require("../environment/index");
const { log } = require("./logger");
const { checkIfAnyEmpty } = require("../utils/helpers");

let staticData = '{"targetsMap":{},"trackerMap":[],"devices":[],"sources":{}}';

async function fetchStaticData() {
  try {
    const params = {
      FunctionName: config.AWS.TRACKERS_STATIC_DATA_LAMBDA,
      InvocationType: "RequestResponse",
      LogType: "Tail",
      Qualifier: "provisioned",
    };
    return new Promise(async (resolve, reject) => {
      lambda.invoke(params, (error, data) => {
        if (error) {
          log.error(
            "Error while calling Lambda function",
            JSON.stringify(error)
          );
          resolve(null);
        }
        var payload = JSON.parse(data.Payload);
        resolve(payload.body);
      });
    });
  } catch (error) {
    log.warn(`Failed to fetch staticData from trackers`);
    log.warn(JSON.stringify(error));
    return null;
  }
}

async function getStaticData() {
  let staticDataJson = JSON.parse(staticData || "{}");
  if (!checkIfAnyEmpty(staticDataJson)) {
    return staticDataJson;
  }
  const responseString = await fetchStaticData();
  const response = JSON.parse(responseString);
  if (response?.success) {
    const { targetsMap, trackerMap, devices, sources } = response.data;
    Object.assign(staticDataJson, {
      targetsMap,
      trackerMap,
      devices,
      sources,
    });
    staticData = JSON.stringify(staticDataJson);
    log.info(response?.message || `Successfully fetched static data`);
  } else {
    log.warn(`Success: false, Failed to fetch staticData from trackers`);
  }
  return staticDataJson;
}

async function getStaticTargetsMap() {
  const staticData = await getStaticData();
  return staticData?.targetsMap || {};
}

async function getStaticTrackers() {
  const staticData = await getStaticData();
  return staticData?.trackerMap || [];
}

module.exports = {
  getStaticData,
  staticData,
  getStaticTargetsMap,
  getStaticTrackers,
};
