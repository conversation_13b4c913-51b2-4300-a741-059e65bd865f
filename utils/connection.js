const mongoose = require("mongoose");
const { Client, Connection } = require("@opensearch-project/opensearch");
const { defaultProvider } = require("@aws-sdk/credential-provider-node");
const aws4 = require("aws4");
const { sign } = aws4;
const { config } = require("../environment/index");
const mongoDBUrl = config.MONGO_DB_URL;
let OpenSearchClient;
let mongoDBConn;

function createAwsConnector(credentials, region) {
  class AmazonConnection extends Connection {
    buildRequestObject(params) {
      const request = super.buildRequestObject(params);
      request.service = "es";
      request.region = region;
      request.headers = request.headers || {};
      request.headers["host"] = request.hostname;

      return sign(request, credentials);
    }
  }
  return {
    Connection: AmazonConnection,
  };
}

async function createOSClient(awsCredentials = null) {
  try {
    let credentials;

    if (awsCredentials) {
      console.log("Using direct AWS credentials");
      credentials = {
        accessKeyId: awsCredentials.accessKeyId,
        secretAccessKey: awsCredentials.secretAccessKey
      };
    } else {
      console.log("Loading AWS credentials from provider chain");
      credentials = await defaultProvider()();
    }

    OpenSearchClient = new Client({
      ...createAwsConnector(credentials, config.REGION),
      node: config.OS_HOST,
    });

    console.log("OpenSearch client created successfully");
    return OpenSearchClient;
  } catch (error) {
    console.error("Failed to create OpenSearch client:", error.message);
    throw error;
  }
}

function getOSClient() {
  return OpenSearchClient;
}

async function connectMongoDB(maxRetries = 5, retryDelay = 2000) {
  if (mongoDBConn == null) {
    console.log('Creating MongoDB connection');
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        mongoDBConn = mongoose.connect(mongoDBUrl, {
          serverSelectionTimeoutMS: 5000,
        }).then(() => mongoose);
        await mongoDBConn;
        console.log(`MongoDB connected successfully on attempt ${attempt}`);
        break; // Successfully connected
      } catch (error) {
        console.error(`MongoDB connection attempt ${attempt} failed:`, error.message);
        if (attempt === maxRetries) {
          console.error('Max MongoDB connection attempts reached.');
          throw error; // Give up after max retries
        }
        console.log(`Retrying MongoDB connection in ${retryDelay / 1000}s...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }
  return mongoDBConn;
};

module.exports = {
  getOSClient,
  createOSClient,
  connectMongoDB,
};
