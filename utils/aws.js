const AWS = require("aws-sdk");
const { config } = require("../environment/index");
const { log: logger } = require("./logger");

AWS.config.update({ region: config.REGION });
const lambda = new AWS.Lambda();

async function sendPushNotification(userGuid, notificationUserGuid, alert, title, type, customData = {}) {
  try {
    const payload = {
      user_guid: userGuid,
      notification_user_guid: notificationUserGuid,
      alert,
      title,
      type,
      custom_data: customData
    };

    const params = {
      FunctionName: config.AWS.PUSH_NOTIFICATIONS_LAMBDA,
      InvocationType: "Event",
      LogType: "Tail",
      Payload: JSON.stringify(payload)
    };

    logger.info(`Calling Notification Lambda, ${JSON.stringify(payload)}`);
    const response = await lambda.invoke(params).promise();
    logger.info(`Notification Lambda Response, ${JSON.stringify(response)}`);
    return true;
  } catch (error) {
    logger.warn(`Error calling sendPushNotification, ${error}`);
    return false;
  }
}

module.exports = {
  sendPushNotification,
};
