/** 
 * Example usage:
 * roundOffNumber(3.14159, 2); // Output: 3.14 (as a number)
 * roundOffNumber("5.6789", 1); // Output: "5.7" (as a string)
 */
function roundOffNumber(num = 0, uptoDigits = 2) {
  num = parseFloat(num);
  const roundedNum = isNaN(num) ? 0 : num.toFixed(uptoDigits);
  return typeof num === 'string' ? roundedNum : parseFloat(roundedNum);
}

/**
 * @param {Number} UTCOffsetMin, offset value from UTC, in minutes e.g. 330 for India
 * @param {string} date, date e.g. "2024-07-14" 
 * @returns startTime & endTime as ISO strings for current day in that time zone
 */
function getTimeRangeByDate(UTCOffsetMin = 0, date = new Date().toISOString().split('T')[0]) {
  const offsetMilliseconds = UTCOffsetMin * 60 * 1000;
  const localMidnight = new Date(new Date(date).getTime() - offsetMilliseconds);
  const startTime = localMidnight.toISOString();
  const endTime = new Date(localMidnight.getTime() + (24 * 60 * 60 * 1000 - 1)).toISOString();
  const timeRange = { startTime, endTime };
  return timeRange;
}

function isEmpty(value) {
  if (typeof value === "object" && value !== null) {
    return Array.isArray(value) ? value.length === 0 : Object.keys(value).length === 0;
  }
  return false;
}

function checkIfAnyEmpty(obj) {
  for (let key in obj) {
    if (isEmpty(obj[key])) {
      return true; // Return true if any property is empty
    }
  }
  return false; // If none of the properties are empty, return false
}

/**
 * 
 * @param {String} timestamp1 as ISO string
 * @param {String} timestamp2 as ISO string
 * @returns difference between timestamps in Minutes
 */
const getTimeDiffInMin = (timestamp1, timestamp2) => {
  const date1 = new Date(timestamp1);
  const date2 = new Date(timestamp2);

  // Calculate the difference in milliseconds
  const diffInMilliseconds = Math.abs(date2 - date1);
  
  // Convert milliseconds to minutes
  const diffInMinutes = diffInMilliseconds / (1000 * 60);
  return diffInMinutes;
};

function getAdjustedDate(dateString, numberOfDays) {
  const date = new Date(dateString);
  date.setDate(date.getDate() + numberOfDays);
  return date.toISOString().split('T')[0]; // Format the date as 'YYYY-MM-DD'
}

function getLocalDateString(timestamp, UTCOffsetMin) {
  const localDate = new Date(new Date(timestamp).getTime() + UTCOffsetMin * 60000);
  const year = localDate.getFullYear();
  const month = String(localDate.getMonth() + 1).padStart(2, '0');
  const day = String(localDate.getDate()).padStart(2, '0');
  const dateString = `${year}-${month}-${day}`;
  return dateString;
}

function getLocalDateTime(timestamp, UTCOffsetMin) {
  const offsetMilliseconds = UTCOffsetMin * 60 * 1000;
  const localDate = new Date(new Date(timestamp).getTime() + offsetMilliseconds).toISOString();
  return localDate;
}

module.exports = {
  checkIfAnyEmpty,
  roundOffNumber,
  getTimeRangeByDate,
  getTimeDiffInMin,
  getAdjustedDate,
  getLocalDateString,
  getLocalDateTime,
};
