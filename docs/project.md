# Target Achievement Project Overview

## Introduction

The Target Achievement service is a critical component of the 20Degrees health and fitness tracking platform. It processes user health data from various trackers and computes achievement metrics against user-defined targets.

## Purpose

The primary purpose of this service is to:

1. Calculate how well users are meeting their health and fitness goals
2. Process data from multiple tracking sources and devices
3. Provide achievement metrics that can be used for user feedback and motivation
4. Support a wide range of health metrics including activity, sleep, vitals, and body measurements

## Key Features

- **Multi-tracker Support**: Processes data from various health trackers (activity, sleep, vitals, etc.)
- **Target Achievement Calculation**: Computes achievement percentages based on target values and thresholds
- **Time Zone Awareness**: Handles user time zones for accurate daily calculations
- **Device-specific Processing**: Supports multiple devices per user and tracker type
- **Flexible Target Types**: Supports different target types (sum, average, percentage, etc.)
- **Favorability Handling**: Processes both positive and negative favorability targets

## System Components

### Core Components

1. **Lambda Handler (index.js)**
   - Entry point for the AWS Lambda function
   - Processes incoming SQS messages
   - Coordinates the target achievement computation process

2. **Target Achievement Service (service/target_achieved.js)**
   - Core business logic for computing target achievements
   - Handles different target types and calculation methods
   - Stores computed achievements in OpenSearch

3. **Logs Service (service/logs.js)**
   - Retrieves user logs from OpenSearch
   - Maps tracker IDs to appropriate indices
   - Provides filtering by device ID and date range

4. **Targets Service (service/targets.js)**
   - Retrieves user target settings
   - Provides the latest active targets for computation

5. **User Profile Service (service/userProfile.js)**
   - Retrieves user profile information
   - Provides time zone information for date calculations

6. **Trackers Service (service/trackers.js)**
   - Retrieves tracker metadata
   - Groups trackers by categories

### Utility Components

1. **Connection Utility (utils/connection.js)**
   - Manages OpenSearch client connections
   - Handles AWS authentication for OpenSearch

2. **Static Data Utility (utils/staticData.js)**
   - Caches static data from external services
   - Retrieves tracker and target metadata

3. **Helpers Utility (utils/helpers.js)**
   - Provides common utility functions
   - Handles date/time calculations and number formatting

4. **Logger Utility (utils/logger.js)**
   - Provides structured logging
   - Configures logging based on environment

## Data Flow

1. The service receives an SQS message containing:
   - User ID
   - Date for calculation
   - Tracker ID to Device ID mapping
   - Timestamp

2. The service retrieves the user's time zone information

3. For each tracker ID in the mapping:
   - Retrieves relevant logs for the specified date range
   - Retrieves the user's targets associated with the tracker
   - Computes achievement metrics for each target
   - Stores the achievement results in OpenSearch

4. The service returns a response with the computation results

## Target Types and Calculations

The service supports various target types:

1. **Sum Targets**: Calculate the sum of log values (e.g., total steps, total water intake)
2. **Average Targets**: Calculate the average of log values (e.g., average heart rate)
3. **Range Targets**: Check if values fall within a specified range (e.g., blood pressure)
4. **Threshold Targets**: Check if values meet or exceed a threshold (e.g., sleep duration)
5. **Count Targets**: Count the number of logs meeting certain criteria (e.g., exercise sessions)

## Deployment and Environments

The service supports multiple deployment environments:

1. **Development**: For development and testing
2. **Production**: For production use
3. **Production Test**: For testing in a production-like environment

Deployment is managed through AWS CodeBuild and CodePipeline, with environment-specific configurations.

## Testing

The service includes a local testing framework using lambda-local, which allows for:

- Local execution of the Lambda function
- Testing with sample event data
- Debugging and development without deploying to AWS

## Future Enhancements

Potential future enhancements for the service include:

1. Support for more complex target types and calculations
2. Improved performance for large data volumes
3. Enhanced error handling and recovery mechanisms
4. Support for real-time achievement updates
5. Integration with notification systems for achievement alerts
