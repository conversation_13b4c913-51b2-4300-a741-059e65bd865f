# Target Achievement Service Architecture

This document outlines the architecture of the Target Achievement service, which is responsible for computing user target achievements based on health and fitness tracking data.

## System Overview

The Target Achievement service is an AWS Lambda function that processes user tracking data to calculate achievement metrics against predefined targets. It integrates with OpenSearch for data storage and retrieval.

```mermaid
graph TD
    A[SQS Queue] -->|Triggers| B[Target Achievement Lambda]
    B -->|Queries| C[OpenSearch]
    B -->|Calls| D[Trackers Static Data Lambda]
    B -->|Stores| E[Target Achievements Index]
    F[External Services] -->|Send Data| A
    
    subgraph "Data Sources"
        C
        D
    end
    
    subgraph "Storage"
        E
    end
```

## Component Architecture

The service follows a modular architecture with clear separation of concerns:

```mermaid
graph TD
    A[index.js] -->|Main Handler| B[service/target_achieved.js]
    B -->|Get User Profile| C[service/userProfile.js]
    B -->|Get Target Data| D[service/targets.js]
    B -->|Get Tracker Data| E[service/trackers.js]
    B -->|Get Log Data| F[service/logs.js]
    
    A -->|Initialize| G[utils/connection.js]
    B -->|Use| H[utils/staticData.js]
    B -->|Use| I[utils/helpers.js]
    B -->|Use| J[utils/logger.js]
    
    subgraph "Core Services"
        B
        C
        D
        E
        F
    end
    
    subgraph "Utilities"
        G
        H
        I
        J
    end
```

## Data Flow

The following diagram illustrates the data flow through the system:

```mermaid
sequenceDiagram
    participant SQS as SQS Queue
    participant Lambda as Target Achievement Lambda
    participant UserProfile as User Profile Service
    participant Logs as Logs Service
    participant Targets as Targets Service
    participant OpenSearch as OpenSearch
    
    SQS->>Lambda: Send message with userId, date, trackerIdDeviceIdMapping
    Lambda->>UserProfile: Get user's UTC offset
    UserProfile->>OpenSearch: Query user profile
    OpenSearch->>UserProfile: Return UTC offset
    Lambda->>Logs: Get logs for date range
    Logs->>OpenSearch: Query logs
    OpenSearch->>Logs: Return logs
    Lambda->>Targets: Get latest targets
    Targets->>OpenSearch: Query targets
    OpenSearch->>Targets: Return targets
    Lambda->>Lambda: Compute target achievements
    Lambda->>OpenSearch: Store target achievements
    Lambda->>SQS: Return response
```

## Deployment Architecture

The service is deployed using AWS CodePipeline and CodeBuild:

```mermaid
graph TD
    A[GitHub Repository] -->|Trigger| B[AWS CodePipeline]
    B -->|Build| C[AWS CodeBuild]
    C -->|Deploy| D[AWS Lambda]
    
    subgraph "CI/CD Pipeline"
        B
        C
    end
    
    subgraph "Runtime Environment"
        D
        E[Amazon SQS]
        F[Amazon OpenSearch]
    end
    
    D -->|Consumes| E
    D -->|Queries/Stores| F
```

## Environment Configuration

The service supports multiple environments:

```mermaid
graph TD
    A[environment/index.js] -->|Load| B[environment/development.js]
    A -->|Load| C[environment/production.js]
    A -->|Load| D[environment/production_test.js]
    
    B -->|Configure| E[Development Environment]
    C -->|Configure| F[Production Environment]
    D -->|Configure| G[Production Test Environment]
```

## Database Schema

The service interacts with several OpenSearch indices:

```mermaid
erDiagram
    USER-PROFILES ||--o{ USER-TARGETS : has
    USER-PROFILES ||--o{ LOGS : has
    USER-TARGETS ||--o{ USER-TARGETS-ACHIEVEMENTS : achieves
    LOGS ||--o{ USER-TARGETS-ACHIEVEMENTS : contributes-to
    
    USER-PROFILES {
        string userId
        number UTCOffsetMin
    }
    
    USER-TARGETS {
        string userId
        string targetId
        number value
        boolean isActive
        number threshold
    }
    
    LOGS {
        string userId
        string deviceId
        timestamp timestamp
        number value
    }
    
    USER-TARGETS-ACHIEVEMENTS {
        string userId
        string targetId
        string date
        number value
        number percentage
        boolean isMet
        array logIds
    }
```
