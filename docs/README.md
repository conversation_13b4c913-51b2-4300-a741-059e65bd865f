# Target Achievement Documentation

This folder contains comprehensive documentation for the Target Achievement service.

## Contents

- [Architecture Documentation](architecture.md) - Detailed system architecture with diagrams
- [Project Overview](project.md) - Project purpose, features, and components

## Diagrams

All diagrams are created using Mermaid, a markdown-based diagramming tool. The diagrams are embedded directly in the markdown files and will render automatically in compatible markdown viewers.

## How to View

For the best viewing experience:

1. Use a markdown viewer that supports Mermaid diagrams
2. GitHub natively supports Mermaid diagrams in markdown files
3. VS Code with the Markdown Preview Mermaid Support extension

## Documentation Updates

This documentation should be kept up-to-date as the project evolves. When making significant changes to the codebase, please update the relevant documentation files.
