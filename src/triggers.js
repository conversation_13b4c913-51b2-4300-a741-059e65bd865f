const { triggerService } = require("../service/triggers");
const logsService = require("../service/logs");
const { devicesService } = require("../service/devices");
const { config } = require("../environment/index");
const { asyncHandler, getDayTimeRange } = require("../utils/helpers");
const { trackersService } = require("../service/trackers");
const { getUTCOffsetValue } = require("../service/userProfile");
const { log: logger } = require("../utils/logger");
const {
  getCGMScoreDetails,
  BASELINE_DURATION,
} = require("../utils/cgmScoring");
const targetService = require("../service/targets");

const CGM_TARGET_ID = 40;
const DEFAULT_CGM_TARGET_VALUE = 120;

const DEFAULT_VERSION = config.triggers.defaultScoreVersion;

async function computeTriggerScores(userId, startTime, endTime, deviceId) {
  try {
    logger.info(`Computing trigger scores for user: ${userId}, deviceId: ${deviceId}, timeRange: ${startTime} to ${endTime}`);

    // Fetch EGV logs from startTime to endTime by deviceId
    const egvLogs = await logsService.getAllLogsByDateRange(userId, config.INDEX.egvs, startTime, endTime, true, [deviceId]);
    
    // Fetch trigger logs between startTime and endTime
    const triggerLogs = await triggerService.getAllTriggers(userId, startTime, endTime);

    if (!triggerLogs || triggerLogs.length === 0) {
      logger.info(`No trigger logs found for user: ${userId} in time range`);
      return { success: true, message: "No trigger logs to process", processedCount: 0 };
    }

    logger.info(`Found ${triggerLogs.length} trigger logs and ${egvLogs.length} EGV logs`);

    // Get CGM target value
    const cgmTargets = await targetService.getLatestTargetsByIds(userId, [CGM_TARGET_ID]);
    const cgmTargetValue = cgmTargets[CGM_TARGET_ID]?.value || DEFAULT_CGM_TARGET_VALUE;

    let processedCount = 0;
    let scoresComputed = 0;

    // Process each trigger log
    for (const triggerLog of triggerLogs) {
      const { startedAt, expiresAt, id: triggerId } = triggerLog;

      // Don't Skip if score already computed
      // if (scoreComputed) {
      //   logger.debug(`Score already computed for trigger: ${triggerId}`);
      //   continue;
      // }

      // Check if any CGM data is available between startedAt and expiresAt
      const cgmDataStartTime = new Date(new Date(startedAt) - BASELINE_DURATION * 60000).toISOString();
      const cgmDataForScoring = egvLogs.filter(egv => {
        const egvTimestamp = new Date(egv.timestamp);
        const windowStart = new Date(cgmDataStartTime);
        const windowEnd = new Date(expiresAt);
        return egvTimestamp >= windowStart && egvTimestamp <= windowEnd;
      });

      if (cgmDataForScoring.length === 0) {
        logger.debug(`No CGM data available for trigger: ${triggerId} in window ${startedAt} to ${expiresAt}`);
        continue;
      }
      logger.info(`Processing trigger: ${triggerId} with ${cgmDataForScoring.length} CGM readings`);
      // Calculate score and CGM details
      const mealStartTime = new Date(startedAt);
      const scoreObj = getCGMScoreDetails(cgmDataForScoring, mealStartTime, cgmTargetValue);

      // Store CGM score in database
      const scoreDoc = getScoreDoc(userId, triggerId, scoreObj, startedAt, expiresAt, DEFAULT_VERSION);
      const scoreDocId = await triggerService.upsertScore(userId, scoreDoc);

      if (scoreDocId) {
        // Update scoreComputed flag to true
        await triggerService.updateTrigger(triggerId, { scoreComputed: true });
        scoresComputed++;
        logger.info(`Score computed and stored for trigger: ${triggerId}`);
      } else {
        logger.error(`Failed to store score for trigger: ${triggerId}`);
      }

      processedCount++;
    }

    logger.info(`Processed ${processedCount} triggers, computed ${scoresComputed} scores`);
    return {
      success: true,
      message: `Processed ${processedCount} triggers, computed ${scoresComputed} scores`,
      processedCount,
      scoresComputed
    };

  } catch (error) {
    logger.error(`Error in computeTriggerScores: ${JSON.stringify(error)}`);
    return [];
  }
}

function getScoreDoc(userId, triggerId, scoreObj, startedAt, expiresAt, version = DEFAULT_VERSION) {
  const doc = {
    userId,
    triggerId,
    startedAt,
    expiresAt,
    metrics: scoreObj,
    score: scoreObj.score.final,
    computedAt: new Date().toISOString(),
    version,
  };
  return doc;
}

module.exports = {
  computeTriggerScores,
};
