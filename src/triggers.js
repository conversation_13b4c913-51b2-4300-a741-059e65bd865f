const { triggerService } = require("../service/triggers");
const logsService = require("../service/logs");
const { devicesService } = require("../service/devices");
const { config } = require("../environment/index");
const { asyncHandler, getDayTimeRange } = require("../utils/helpers");
const { trackersService } = require("../service/trackers");
const { getUTCOffsetValue } = require("../service/userProfile");
const { log: logger } = require("../utils/logger");
const {
  getCGMScoreDetails,
  generateCGMDetailsResponse,
  BASELINE_DURATION,
} = require("../utils/cgmScoring");
const targetService = require("../service/targets");

const Ajv = require("ajv");
const addFormats = require("ajv-formats");

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);
const schema = require("../models/triggers.json");
const { getSignedURL } = require("../utils/aws");

const validate = ajv.compile(schema);

const mealLogTrackerId = 1,
  activityTrackerId = 4,
  sleepTrackerId = 5,
  cgmTrackerId = 8;
const CGM_TARGET_ID = 40;
const DEFAULT_CGM_TARGET_VALUE = 120;

const DEFAULT_VERSION = config.triggers.defaultScoreVersion;
const WINDOW_EXTENSION_DURATION_IN_HOURS =
  config.triggers.windowExtensionDurationInHours;

async function computeTriggerScores(userId, egvsTrackerId, startTime, endTime) {
  
}

module.exports = {
  computeTriggerScores,
};
