version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 12
    commands:
      - npm install
      - npm install -g bestzip
      - bestzip archive.zip .
      - aws s3 cp archive.zip s3://healthtechgate-codepipeline-files/lambda-target-achievements-dev.zip
      - aws lambda update-function-code --function-name target-achievements-dev --s3-bucket healthtechgate-codepipeline-files --s3-key lambda-target-achievements-dev.zip