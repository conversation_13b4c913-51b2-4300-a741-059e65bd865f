#!/bin/bash

# <PERSON>ript to upload environment variables to AWS Secrets Manager
# Usage: ./upload-env-to-aws.sh --env [development|sandbox|production]

# Default values
ENV="sandbox"
SECRET_NAME="20degrees-target-achievements"
AWS_PROFILE=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --env)
      ENV="$2"
      shift 2
      ;;
    --secret-name)
      SECRET_NAME="$2"
      shift 2
      ;;
    --profile)
      AWS_PROFILE="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Validate environment
if [[ "$ENV" != "development" && "$ENV" != "sandbox" && "$ENV" != "production" ]]; then
  echo "Invalid environment: $ENV. Must be one of: development, sandbox, production"
  exit 1
fi

# Set AWS profile based on environment if not explicitly provided
if [[ -z "$AWS_PROFILE" ]]; then
  case $ENV in
    development)
      AWS_PROFILE="development"
      ;;
    sandbox)
      AWS_PROFILE="sandbox"
      ;;
    production)
      AWS_PROFILE="production"
      ;;
  esac
fi

# Set the full secret name with environment
FULL_SECRET_NAME="${SECRET_NAME}-${ENV}"

# Path to the environment file
ENV_FILE="./environment/env/.env.${ENV}"

# Check if the environment file exists
if [[ ! -f "$ENV_FILE" ]]; then
  echo "Environment file not found: $ENV_FILE"
  exit 1
fi

# Convert .env file to JSON
echo "Converting $ENV_FILE to JSON..."
JSON_CONTENT="{"
while IFS='=' read -r key value || [[ -n "$key" ]]; do
  # Skip comments and empty lines
  if [[ "$key" =~ ^#.*$ || -z "$key" ]]; then
    continue
  fi
  
  # Remove quotes from value if present
  value=$(echo "$value" | sed -e 's/^"//' -e 's/"$//')
  
  # Add to JSON
  JSON_CONTENT="${JSON_CONTENT}\"${key}\":\"${value}\","
done < "$ENV_FILE"

# Remove trailing comma and close JSON object
JSON_CONTENT="${JSON_CONTENT%,}}"

# Create a temporary file for the JSON content
TMP_FILE=$(mktemp)
echo "$JSON_CONTENT" > "$TMP_FILE"

# Check if the secret already exists
echo "Checking if secret $FULL_SECRET_NAME already exists..."
SECRET_EXISTS=$(aws secretsmanager list-secrets --profile "$AWS_PROFILE" --query "SecretList[?Name=='$FULL_SECRET_NAME'].Name" --output text)

if [[ -n "$SECRET_EXISTS" ]]; then
  # Update existing secret
  echo "Updating existing secret: $FULL_SECRET_NAME"
  aws secretsmanager update-secret --profile "$AWS_PROFILE" \
    --secret-id "$FULL_SECRET_NAME" \
    --secret-string file://"$TMP_FILE"
else
  # Create new secret
  echo "Creating new secret: $FULL_SECRET_NAME"
  aws secretsmanager create-secret --profile "$AWS_PROFILE" \
    --name "$FULL_SECRET_NAME" \
    --description "Environment variables for $ENV environment" \
    --secret-string file://"$TMP_FILE"
fi

# Clean up temporary file
rm "$TMP_FILE"

echo "Environment variables successfully uploaded to AWS Secrets Manager as $FULL_SECRET_NAME"
