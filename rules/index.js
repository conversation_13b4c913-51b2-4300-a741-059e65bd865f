const { NudgeRule } = require('../src/nudgeEngine');
const { getAdjustedDate, getLocalDateString } = require("../utils/helpers");

class SleepMealTimingRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("sleep_meal_gap", "Encourage early dinner before sleep.");
    this.minGapHours = params.minGapHours;
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    // Get sleep and meal data from target achievements
    const sleepAchievements = data.formattedTargetAchievements[data.targetIds.sleep] || []; 
    const mealAchievements = data.formattedTargetAchievements[data.targetIds.meallog] || []; 

    const { endDate } = data.dateTimeData;  // endDate == today's date

    // Get today's sleep and meal data
    const todaySleep = sleepAchievements.find(achievement => achievement.date === endDate);
    const todayMeal = mealAchievements.find(achievement => achievement.date === endDate);

    if (!todaySleep?.metaData?.sleepStartTime || !todayMeal?.metaData?.lastMealTimestamp) {
      return false;
    }

    const sleepTime = new Date(todaySleep.metaData.sleepStartTime);
    const lastMealTime = new Date(todayMeal.metaData.lastMealTimestamp);

    const gap = (sleepTime - lastMealTime) / (1000 * 60 * 60); // hours
    return (gap > 0) && (gap < this.minGapHours);
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "sleep";
    const title = "Too Close to Bedtime?";
    const message = `Try to finish your last meal at least ${this.minGapHours} hours before bedtime.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class BedtimeVariabilityRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("bedtime_variability", "Monitor bedtime consistency for better sleep quality.");
    this.shiftThresholdHrs = params.shiftThresholdHrs;
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const sleepAchievements = data.formattedTargetAchievements[data.targetIds.sleep] || [];
    const { startDate, endDate } = data.dateTimeData;

    // Get last 7 days of sleep data
    const weekSleepData = sleepAchievements.filter(
      (achievement) =>
        achievement.date >= startDate &&
        achievement.date <= endDate &&
        achievement.metaData?.sleepStartTime
    );

    if (weekSleepData.length < 7) return false; // Need 7 days of data

    // Extract sleep start times and calculate variability
    const sleepTimes = weekSleepData.map((achievement) => {
      const sleepTime = new Date(achievement.metaData.sleepStartTime);
      return sleepTime.getHours() + sleepTime.getMinutes() / 60; // Convert to decimal hours
    });

    const maxTime = Math.max(...sleepTimes);
    const minTime = Math.min(...sleepTimes);
    const variability = maxTime - minTime;

    return variability > this.shiftThresholdHrs;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "sleep";
    const title = "Inconsistent Bedtime";
    const message = `Your bedtime has varied by more than ${this.shiftThresholdHrs} hours this week. A consistent sleep schedule helps improve recovery.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class LateNightMealRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("late_meal_night", "Encourage earlier meal timing for better health.");
    this.lateHour = params.lateHour;
    this.daysThreshold = params.daysThreshold;
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const mealAchievements = data.formattedTargetAchievements[data.targetIds.meallog] || [];
    const { startDate, endDate } = data.dateTimeData;
    const { UTCOffsetMin } = data;

    // Get last 7 days of meal data
    const weekMealData = mealAchievements.filter(achievement =>
      achievement.date >= startDate && achievement.date <= endDate &&
      achievement.metaData?.lastMealTimestamp
    );

    if (weekMealData.length === 0) return false;

    // Count days with late meals
    let lateMealDays = 0;

    weekMealData.forEach(achievement => {
      const lastMealTime = new Date(achievement.metaData.lastMealTimestamp);
      // Convert UTC to local time using UTCOffset
      const localTime = new Date(lastMealTime.getTime() + UTCOffsetMin * 60 * 1000);
      const mealHour = localTime.getHours();

      if (mealHour >= this.lateHour) {
        lateMealDays++;
      }
    });

    return lateMealDays >= this.daysThreshold;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "nutrition";
    const title = "Late Night Eating Pattern";
    const message = `You've been logging late-night meals past ${this.lateHour}:00 on ${this.daysThreshold} or more days. Consider lighter meals earlier in the evening.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class MissedTargetSevenDaysRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("missed_target_seven_day", "Encourage users to get back on track after missing targets for 7 days.");
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const { formattedTargetAchievements, activeTargetIds, dateTimeData } = data;
    const { endDate } = dateTimeData;
    const yesterday = getAdjustedDate(endDate, -1);

    // Get last 7 days
    const last7DaysExceptToday = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(yesterday);
      date.setDate(date.getDate() - i);
      last7DaysExceptToday.push(date.toISOString().split('T')[0]);
    }

    // Check if any active target has been missed for all 7 days
    for (const targetId of activeTargetIds) {
      const achievements = formattedTargetAchievements[targetId] || [];

      let consecutiveMisses = 0;
      for (const date of last7DaysExceptToday) {
        const dayAchievement = achievements.find(a => a.date === date);
        if (!dayAchievement || !dayAchievement.isMet) {
          consecutiveMisses++;
        } else {
          break; // Reset if target was met
        }
      }

      if (consecutiveMisses >= 7) {
        return true;
      }
    }

    return false;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "motivation";
    const title = "7-Day Target Miss";
    const message = "It looks like you've missed your daily habits for 7 days in a row. Would you like to update your habits or plans?";
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class MissedTargetThreeDaysRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("missed_target_three_day", "Encourage users to get back on track after missing targets for 3 days.");
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const { formattedTargetAchievements, activeTargetIds, dateTimeData } = data;
    const { endDate } = dateTimeData;
    const yesterday = getAdjustedDate(endDate, -1);

    // Get last 3 days
    const last3DaysExceptToday = [];
    for (let i = 2; i >= 0; i--) {
      const date = new Date(yesterday);
      date.setDate(date.getDate() - i);
      last3DaysExceptToday.push(date.toISOString().split('T')[0]);
    }

    // Check if any active target has been missed for all 3 days
    for (const targetId of activeTargetIds) {
      const achievements = formattedTargetAchievements[targetId] || [];

      let consecutiveMisses = 0;
      for (const date of last3DaysExceptToday) {
        const dayAchievement = achievements.find(a => a.date === date);
        if (!dayAchievement || !dayAchievement.isMet) {
          consecutiveMisses++;
        } else {
          break; // Reset if target was met
        }
      }

      if (consecutiveMisses >= 3) {
        return true;
      }
    }

    return false;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "motivation";
    const title = "3-Day Target Miss";
    const message = "You've fallen short of one or more daily habits for 3 straight days. Let's get back on track!";
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class GoalUnachievableTrendRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("unattainable_goal_trend", "Suggest goal adjustments when trends indicate unattainability.");
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const { formattedTargetAchievements, activeTargetIds, dateTimeData } = data;
    const { activeWindowStartDate, activeWindowEndDate, prevActiveWindowStartDate, prevActiveWindowEndDate } = dateTimeData;

    if (!activeWindowStartDate || !prevActiveWindowStartDate) return false;

    // Compare current active window vs previous active window achievements
    for (const targetId of activeTargetIds) {
      const achievements = formattedTargetAchievements[targetId] || [];

      // Get achievements for current and previous active windows
      const currentWindowAchievements = achievements.filter(a =>
        a.date >= activeWindowStartDate && a.date <= activeWindowEndDate && a.target !== null
      );
      const prevWindowAchievements = achievements.filter(a =>
        a.date >= prevActiveWindowStartDate && a.date <= prevActiveWindowEndDate && a.target !== null
      );

      if (currentWindowAchievements.length < 3 || prevWindowAchievements.length < 3) {
        // No considerable data available for trend analysis
        continue;
      }
        
      // Calculate average achievement percentages
      const currentAvg = currentWindowAchievements.reduce((sum, a) => sum + a.percentage, 0) / currentWindowAchievements.length;
      const prevAvg = prevWindowAchievements.reduce((sum, a) => sum + a.percentage, 0) / prevWindowAchievements.length;

      // If current performance is significantly worse and below 50%
      if (currentAvg < 50 && currentAvg < prevAvg * 0.8) {
        return true;
      }
    }

    return false;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "motivation";
    const title = "Goal Adjustment Suggestion";
    const message = "Based on your recent trends, your goal may not be reachable within the program timeframe. Let's adjust our goals and win small.";
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class TargetDriftRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("weekly_target_drift", "Alert when weekly averages drift significantly.");
    this.driftPercent = params.driftPercent || 50;
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const { formattedTargetAchievements, activeTargetIds, dateTimeData } = data;
    const { endDate } = dateTimeData;
    const yesterday = getAdjustedDate(endDate, -1);

    // Get current week (last 7 days) and previous week
    const currentWeekEnd = yesterday;
    const currentWeekStart = new Date(currentWeekEnd);
    currentWeekStart.setDate(currentWeekStart.getDate() - 6);

    const prevWeekEnd = new Date(currentWeekStart);
    prevWeekEnd.setDate(prevWeekEnd.getDate() - 1);
    const prevWeekStart = new Date(prevWeekEnd);
    prevWeekStart.setDate(prevWeekStart.getDate() - 6);

    const currentWeekStartStr = getLocalDateString(currentWeekStart.toISOString(), data.UTCOffsetMin);
    const currentWeekEndStr = currentWeekEnd;
    const prevWeekStartStr = getLocalDateString(prevWeekStart.toISOString(), data.UTCOffsetMin);
    const prevWeekEndStr = getLocalDateString(prevWeekEnd.toISOString(), data.UTCOffsetMin);

    for (const targetId of activeTargetIds) {
      const achievements = formattedTargetAchievements[targetId] || [];

      // Get achievements for both weeks
      const currentWeekAchievements = achievements.filter(a =>
        a.date >= currentWeekStartStr && a.date <= currentWeekEndStr && a.value !== null
      );
      const prevWeekAchievements = achievements.filter(a =>
        a.date >= prevWeekStartStr && a.date <= prevWeekEndStr && a.value !== null
      );

      if (currentWeekAchievements.length < 3 || prevWeekAchievements.length < 3) {
        // Not enough data to make a comparison
        continue;
      }
      // Calculate weekly averages
      const currentWeekAvg = currentWeekAchievements.reduce((sum, a) => sum + a.value, 0) / currentWeekAchievements.length;
      const prevWeekAvg = prevWeekAchievements.reduce((sum, a) => sum + a.value, 0) / prevWeekAchievements.length;

      if (prevWeekAvg === 0) continue;

      // Calculate percentage change
      const percentChange = Math.abs((currentWeekAvg - prevWeekAvg) / prevWeekAvg) * 100;

      if (percentChange > this.driftPercent) {
        return true;
      }
    }

    return false;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "tracking";
    const title = "Weekly Target Drift";
    const message = this.prompt.replace('{{driftPercent}}', this.driftPercent);
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class MissingTrackerLogsRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("missing_tracker_logs", "Alert when tracker data is missing for multiple days.");
    this.missingDays = params.missingDays || 3;
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const { formattedTargetAchievements, activeTargetIds, dateTimeData } = data;
    const { endDate } = dateTimeData;
    const yesterday = getAdjustedDate(endDate, -1);

    // Get last N days based on missingDays parameter
    const lastNDays = [];
    for (let i = this.missingDays - 1; i >= 0; i--) {
      const date = new Date(yesterday);
      date.setDate(date.getDate() - i);
      lastNDays.push(date.toISOString().split('T')[0]);
    }

    // Check if any active target has missing device data for all N days
    for (const targetId of activeTargetIds) {
      const achievements = formattedTargetAchievements[targetId] || [];
      let missingDeviceDataDays = 0;
      for (const date of lastNDays) {
        const dayAchievement = achievements.find((a) => a.date === date);
        // We are only adding in deviceIds if there is any log from any device
        if (!dayAchievement || !dayAchievement.metaData?.deviceIds || dayAchievement.metaData.deviceIds.length === 0) {
          missingDeviceDataDays++;
        }
      }

      if (missingDeviceDataDays >= this.missingDays) {
        return true;
      }
    }

    return false;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "tracking";
    const title = "Missing Tracker Data";
    const message = `We haven't received data from one or more active trackers for ${this.missingDays} days. Please check device sync or permissions.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class CGMSpikeNoMealRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("cgm_spike_no_meal", "Detect glucose spikes without meal correlation.");
    this.windowMins = params.windowMins;
    this.glucoseDeltaThreshold = params.glucoseDeltaThreshold;
    this.aucThreshold = params.aucThreshold;
    this.peakGlucoseThreshold = params.peakGlucoseThreshold;
    this.recoveryTimeThreshold = params.recoveryTimeThreshold;
    this.prompt = config.prompt;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const CGMData = data.CGMData || [];
    const mealLogsForDay = data.mealLogsForDay || [];

    if (!CGMData || CGMData.length < 4) return false; // Need at least 4 readings for analysis

    // Sort CGM data by timestamp
    const sortedCGMData = CGMData
      .filter(log => log.value && log.timestamp)
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    if (sortedCGMData.length < 4) return false;

    // Analyze only the last 60 minutes
    const currentTime = new Date();
    const sixtyMinutesAgo = new Date(currentTime.getTime() - 60 * 60 * 1000);

    // Get readings from the last 60 minutes
    const last60MinReadings = sortedCGMData.filter(reading => {
      const readingTime = new Date(reading.timestamp);
      return readingTime >= sixtyMinutesAgo && readingTime <= currentTime;
    });

    if (last60MinReadings.length < 4) return false;

    // Check if this 60-minute window shows a spike pattern
    const spike = this.analyzeSpikePattern(sortedCGMData);

    if (!spike) return false;

    // Check if it's likely an unlogged meal
    if (this.isLikelyUnloggedMeal(spike, mealLogsForDay)) {
      // Store the spike details for the nudge message
      data.detectedSpike = spike;
      return true;
    }

    return false;
  }

  analyzeSpikePattern(glucoseData) {
    // Use the first reading as baseline
    const baseline = glucoseData[0].value;
    const startTime = new Date(glucoseData[0].timestamp);

    // Find peak glucose in the 60-minute window
    const peakGlucose = Math.max(...glucoseData.map(r => r.value));
    const glucoseDelta = peakGlucose - baseline;

    // Calculate AUC over the window
    const aucOverWindow = this.calculateAUC(glucoseData, baseline);

    // Calculate recovery time
    const recoveryTime = this.calculateRecoveryTime(glucoseData, baseline, startTime);

    // Check if this pattern meets spike criteria
    if (glucoseDelta >= this.glucoseDeltaThreshold &&
        aucOverWindow >= this.aucThreshold &&
        peakGlucose >= this.peakGlucoseThreshold &&
        recoveryTime > this.recoveryTimeThreshold) {

      return {
        startTime: startTime,
        peakTime: glucoseData.find(r => r.value === peakGlucose)?.timestamp,
        baseline: baseline,
        peakGlucose: peakGlucose,
        glucoseDelta: glucoseDelta,
        aucOverWindow: aucOverWindow,
        recoveryTime: recoveryTime
      };
    }

    return null; // No spike pattern detected
  }

  calculateAUC(glucoseData, baseline) {
    let auc = 0;
    for (let i = 1; i < glucoseData.length; i++) {
      const current = glucoseData[i];
      const previous = glucoseData[i - 1];
      const duration = (new Date(current.timestamp) - new Date(previous.timestamp)) / (1000 * 60); // minutes

      // Calculate area above baseline
      const currentAboveBaseline = Math.max(0, current.value - baseline);
      const previousAboveBaseline = Math.max(0, previous.value - baseline);
      auc += ((currentAboveBaseline + previousAboveBaseline) / 2) * duration;
    }
    return auc;
  }

  calculateRecoveryTime(glucoseData, baseline, startTime) {
    const recoveryThreshold = baseline * (1 + 10 / 100); // 10% above baseline

    // Find the last reading that's still above recovery threshold
    for (let i = glucoseData.length - 1; i >= 0; i--) {
      const reading = glucoseData[i];
      if (reading.value > recoveryThreshold) {
        const recoveryTime = (new Date(reading.timestamp) - startTime) / (1000 * 60); // minutes
        return Math.max(0, recoveryTime);
      }
    }

    // If all readings are below threshold, recovery happened before the window
    return 0;
  }

  isLikelyUnloggedMeal(spike, mealLogs) {
    const spikeTime = new Date(spike.startTime);

    // Check for meal logs within the specified window
    const hasNearbyMeal = mealLogs.some(meal => {
      const mealTime = new Date(meal.date);
      const timeDiffMins = Math.abs(spikeTime - mealTime) / (1000 * 60);
      return timeDiffMins <= this.windowMins;
    });

    if (hasNearbyMeal) return false; // Meal is already logged

    return true; // Likely unlogged meal
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "nutrition";
    const title = "Possible Missed Meal Entry";

    // Generate agentic message with spike details if available
    let message;
    if (data.detectedSpike) {
      const spike = data.detectedSpike;
      const spikeTime = new Date(spike.startTime);
      const timeString = spikeTime.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });

      message = this.prompt
        .replace('{{time}}', timeString)
        .replace('{{windowMins}}', this.windowMins);
    } else {
      // Fallback to original message format
      message = `Your glucose spiked and no meal was logged within ${this.windowMins} minutes of the spike. Please log your meals to help us interpret your readings.`;
    }

    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class CGMSpikeThresholdRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("cgm_spike_threshold", "Detect glucose threshold crossings."); 
    this.threshold = params.threshold;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const CGMData = data.CGMData || [];
    if (!CGMData || CGMData.length === 0) return false;

    // Check if any recent reading crossed the threshold
    return CGMData.some(log =>
      log.value && log.value > this.threshold
    );
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "nutrition";
    const title = "Glucose Threshold Crossed";
    const message = `Your glucose crossed ${this.threshold} mg/dL. Let's take a look at how recent meals or activity might be contributing.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}
class NoRecentChatRule extends NudgeRule {
  constructor(params = {}, config = {}) {
    super("no_recent_chat", "Encourage users to reconnect with wellness agent.");
    this.days = params.days;
    this.priority = config.priority;
    this.ttl = config.ttl;
  }

  applies({ data }) {
    const lastChatInteraction = data.lastChatInteraction || null;
    if (!lastChatInteraction) {
      // If no chat interaction found, consider it as needing a nudge
      return true;
    }

    const lastInteractionDate = new Date(lastChatInteraction.timestamp);
    const currentDate = new Date();
    const daysDifference = (currentDate - lastInteractionDate) / (1000 * 60 * 60 * 24);

    return daysDifference > this.days;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "engagement";
    const title = "Reconnect with Your Wellness Agent";
    const message = `You haven't checked in with your wellness agent in over ${this.days} days. Want to reconnect and get personalized guidance?`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}



function getNudgeDocument(userId, category, title, message, priority = 2, ttlSeconds = 86400, ruleId = null) {
  const currentTime = new Date();
  const ttlDate = new Date(currentTime.getTime() + ttlSeconds * 1000);

  const doc = {
    userId,
    type: "nudge",
    content: {
      title,
      message,
    },
    timestamp: currentTime.toISOString(),
    category,
    priority,
    ttl: ttlDate.toISOString(),
    status: "open"
  };

  if (ruleId) {
    doc.ruleId = ruleId;
  }

  return doc;
}

module.exports = {
  SleepMealTimingRule,
  BedtimeVariabilityRule,
  LateNightMealRule,
  MissedTargetSevenDaysRule,
  MissedTargetThreeDaysRule,
  GoalUnachievableTrendRule,
  TargetDriftRule,
  MissingTrackerLogsRule,
  CGMSpikeNoMealRule,
  CGMSpikeThresholdRule,
  NoRecentChatRule,
};
