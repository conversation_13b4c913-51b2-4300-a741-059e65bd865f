const fs = require("fs");
const path = require("path");
const { NudgeEngine } = require("./src/nudgeEngine");
const { SleepMealTimingRule, BedtimeVariabilityRule, LateNightMealRule, MissedTargetSevenDaysRule, MissedTargetThreeDaysRule, GoalUnachievableTrendRule, TargetDriftRule, CGMSpikeNoMealRule, CGMSpikeThresholdRule, MissingTrackerLogsRule, NoRecentChatRule } = require("./rules/index");
const { createOSClient, connectMongoDB } = require("./utils/connection");
const { computeTargetAchievement } = require("./service/target_achieved");
const { getAllDefaultTrackers } = require("./service/trackers");
const { log: logger } = require("./utils/logger");
const { getTimeRangeByDate, getAdjustedDate, getLocalDateString } = require("./utils/helpers");
const { getUserProfile } = require("./service/userProfile");
const { computeTriggerScores } = require("./src/triggers");

const { upsertUserRecommendations, getEligibleNudges, upsertLastSentAtByDocId } = require("./service/recommendations");
const { getActiveWindowData } = require("./service/targetCron");
const { getTargetsAchievedByDateRange } = require("./service/target_achieved");
const { getLatestTargetsByIds } = require("./service/targets");
const { getLastChatInteraction } = require("./service/chatInteraction");
const { getUserMealLogsByDate } = require("./service/meallogs");
const { getStaticTargetsMap } = require("./utils/staticData");
const { trackerMap, getAllLogsByDateRange } = require("./service/logs");
const { sendNotificationByNudgeRuleIds } = require("./service/pushNotifications");

const sleepTargetId = 4;
const mealLogTargetId = 15;

const egvsTrackerId = 8;

// Rule Registry (maps ruleId to class name)
const ruleRegistry = {
  sleep_meal_gap: SleepMealTimingRule,
  bedtime_variability: BedtimeVariabilityRule,
  late_meal_night: LateNightMealRule,
  missed_target_seven_day: MissedTargetSevenDaysRule,
  missed_target_three_day: MissedTargetThreeDaysRule,
  unattainable_goal_trend: GoalUnachievableTrendRule,
  weekly_target_drift: TargetDriftRule,
  cgm_spike_no_meal: CGMSpikeNoMealRule,
  cgm_spike_threshold: CGMSpikeThresholdRule,
  missing_tracker_logs: MissingTrackerLogsRule,
  no_recent_chat: NoRecentChatRule
};

async function loadRuleConfigs() {
  const configPath = path.resolve(
    __dirname,
    "config/nudgeRules.json"
  );
  const data = fs.readFileSync(configPath, "utf-8");
  return JSON.parse(data);
}

async function instantiateRules(relevantTrackerIds = null, userCreationTimeStamp = null) {
  const configs = await loadRuleConfigs();
  const currentDate = new Date();
  const userCreationDate = new Date(userCreationTimeStamp);
  const userAgeDays = Math.floor((currentDate - userCreationDate) / (1000 * 60 * 60 * 24)); // Convert milliseconds to days

  let filteredConfigs = configs.filter((cfg) => {
    // Check if the rule is enabled and registered
    if (!cfg.enabled || !ruleRegistry[cfg.ruleId]) {
      return false;
    }

    // Check if the user's age meets the minimum requirement for the rule
    if (cfg.minUserAgeDays !== undefined && userAgeDays < cfg.minUserAgeDays) {
      return false;
    }

    return true;
  });

  // Further filter rules based on relevant requiredTrackerIds if provided
  if (relevantTrackerIds && relevantTrackerIds.length > 0) {
    filteredConfigs = filteredConfigs.filter((cfg) => {
      const ruleTrackerIds = cfg.requiredTrackerIds;

      // Rules with "none" requiredTrackerIds should always be included (e.g., no_recent_chat)
      if (ruleTrackerIds === "none") {
        return true;
      }

      // Rules with "any" requiredTrackerIds should be included if any tracker is present
      if (ruleTrackerIds === "any") {
        return true;
      }

      // Rules with specific requiredTrackerIds should be included if any of their requiredTrackerIds match
      if (Array.isArray(ruleTrackerIds)) {
        return ruleTrackerIds.some(trackerId => relevantTrackerIds.includes(parseInt(trackerId)));
      }

      return false;
    });
  }

  return filteredConfigs.map((cfg) => new ruleRegistry[cfg.ruleId](cfg.params, cfg));
}

function determineDataRequirements(ruleIds) {
  const requirements = {
    needsDeviceMapping: false,
    needsTargetAchievements: false,
    needsCGMData: false,
    needsMealLogs: false,
    needsChatData: false
  };

  // Rules that need target achievements
  const targetRules = ['sleep_meal_gap', 'bedtime_variability', 'late_meal_night', 'missed_target_seven_day', 'missed_target_three_day', 'unattainable_goal_trend', 'weekly_target_drift', 'missing_tracker_logs'];

  // Rules that need CGM data
  const cgmRules = ['cgm_spike_no_meal', 'cgm_spike_threshold'];

  // Rules that need meal logs (for CGM correlation)
  const mealLogRules = ['cgm_spike_no_meal'];

  // Rules that need chat data
  const chatRules = ['no_recent_chat'];

  // Check if any rule needs target achievements
  if (ruleIds.some(ruleId => targetRules.includes(ruleId))) {
    requirements.needsTargetAchievements = true;
  }

  // Check if any rule needs CGM data
  if (ruleIds.some(ruleId => cgmRules.includes(ruleId))) {
    requirements.needsCGMData = true;
    requirements.needsDeviceMapping = true; // CGM data needs device mapping
  }

  // Check if any rule needs meal logs
  if (ruleIds.some(ruleId => mealLogRules.includes(ruleId))) {
    requirements.needsMealLogs = true;
  }

  // Check if any rule needs chat data
  if (ruleIds.some(ruleId => chatRules.includes(ruleId))) {
    requirements.needsChatData = true;
  }

  // If we need target achievements, we also need device mapping for some trackers
  if (requirements.needsTargetAchievements) {
    requirements.needsDeviceMapping = true;
  }

  return requirements;
}

exports.handler = async (event) => {
  try {
    logger.info(`Received events`);
    event.Records.forEach((r, i) => logger.info(`Parsed Record ${i}:`, JSON.parse(r.body))); 

    const body = JSON.parse(event.Records[0].body);
    logger.info(`Processing request body: ${JSON.stringify(body)}`);

    const { userId, date, trackerIdDeviceIdMapping, time } = body;
    const isCron = body.metadata?.isCron;
    logger.info(`Executing lambda for ${isCron ? 'cron' : 'user'} event`)
    if (!userId || !trackerIdDeviceIdMapping || !time || (!isCron && !date)) {
      logger.error("Missing required parameters in the request body");
      return;
    }

    process.env['AWS_ACCESS_KEY_ID'] = "********************";
    process.env['AWS_SECRET_ACCESS_KEY'] = "BtH1XFJC4MNa2ntsTSpWo7sx1hKl/iW9ghqaIrm2";

    await createOSClient();
    await connectMongoDB();
    const targetAchievementResponse = {};
    
    const userProfile = await getUserProfile(userId);
    const UTCOffsetMin = userProfile?.UTCOffsetMin || 330;
    const userCreationTimeStamp = userProfile?.createdAt; // As ISO string

    if(date) {
      const { startTime, endTime } = getTimeRangeByDate(UTCOffsetMin, date);
      logger.info(`date: ${date} | UTCOffsetMin: ${UTCOffsetMin} | startTime: ${startTime}, endTime: ${endTime}`);
      
      for(const trackerId in trackerIdDeviceIdMapping){
        const deviceId = trackerIdDeviceIdMapping[trackerId];
        const data = await computeTargetAchievement(userId, trackerId, deviceId, date, startTime, endTime);
        targetAchievementResponse[trackerId] = data;
      }
      logger.info(`Response received: ${JSON.stringify(targetAchievementResponse)}`);
    }
    else {
      logger.info(`No date provided, skipping target achievement computation`);
    }
    
    const todaysDate = getLocalDateString(new Date().toISOString(), UTCOffsetMin);
    const yesterdaysDate = getAdjustedDate(todaysDate, -1);
    // Calling Nudge Engine only for today's and yesterday's date or when date is not passed (for cron)
    if(!date || date == todaysDate || date == yesterdaysDate) {
      // Extract requiredTrackerIds from the event for rule filtering
      const relevantTrackerIds = Object.keys(trackerIdDeviceIdMapping).map(id => parseInt(id));
      const nudgeResponse = await calculateNudge(userId, UTCOffsetMin, relevantTrackerIds, userCreationTimeStamp);
      logger.info(`NudgeResponse: ${JSON.stringify(nudgeResponse)}`);

      if(isCron) {
        const allRuleIds = Object.values(nudgeResponse).map(doc => doc.ruleId);
        const eligibleRuleIds = await getEligibleNudges(userId, allRuleIds);
        const notificationResponse = await sendNotificationByNudgeRuleIds(userId, eligibleRuleIds);
        logger.info(`NotificationResponse: ${JSON.stringify(notificationResponse)}`);
        for (const [ruleId, success] of Object.entries(notificationResponse)) {
          if (success) {
            const docId = Object.entries(nudgeResponse).find(([, v]) => v.ruleId === ruleId)?.[0];
            if (docId) {
              await upsertLastSentAtByDocId(docId);
            }
          }
        }             
      }
    }
    else {
      logger.info(`Skipping nudge calculation for older date: ${date}`);
    }

    if(date && Object.keys(trackerIdDeviceIdMapping).includes(egvsTrackerId.toString())) {
      logger.info(`Trigger window calculations started`);
      const { startTime, endTime } = getTimeRangeByDate(UTCOffsetMin, date);
      const computeTriggerScoresResponse = await computeTriggerScores(userId, startTime, endTime, trackerIdDeviceIdMapping[egvsTrackerId]);
      logger.info(`computeTriggerScoresResponse: ${JSON.stringify(computeTriggerScoresResponse)}`);
    }

    logger.info("Successfully executed lambda");
    return;
  } catch (error) {
    logger.error(`Error in handler: ${JSON.stringify(error)}`);
    return;
  }
};

async function calculateNudge(userId, UTCOffsetMin, relevantTrackerIds = null, userCreationTimeStamp) {
  const userData = { userId };
  const rules = await instantiateRules(relevantTrackerIds, userCreationTimeStamp);
  const engine = new NudgeEngine(rules);

  logger.info(`Calculating nudges for trackerIds: ${JSON.stringify(relevantTrackerIds)}`);
  logger.info(`Loaded ${rules.length} relevant rules: ${rules.map(r => r.ruleId).join(', ')}`);

  // Determine what data is needed based on the rules that will be executed
  const ruleIds = rules.map(r => r.ruleId);
  const dataRequirements = determineDataRequirements(ruleIds);
  logger.info(`Data requirements: ${JSON.stringify(dataRequirements)}`);

  const date = new Date(); // current date e.g. 2025-06-05T10:45:00.000Z
  const endDate = getLocalDateString(date.toISOString(), UTCOffsetMin); // 2025-06-05
  const startDate = getAdjustedDate(endDate, -6);// 2025-05-30

  // Always fetch basic date/time data and window data
  const activeWindowData = await getActiveWindowData(userId); // e.g. 2025-06-02 & 2025-06-08
  const { activeWindowStartDate, activeWindowEndDate } = activeWindowData || {};
  const prevActiveWindowStartDate = getAdjustedDate(activeWindowStartDate, -7); // 2025-05-26
  const prevActiveWindowEndDate = getAdjustedDate(activeWindowEndDate, -7); // 2025-06-01

  // Conditionally fetch data based on requirements
  let defaultDevicePerTrackerId = {};
  let formattedTargetAchievements = {};
  let CGMData = [];
  let mealLogsForDay = [];
  let lastChatInteraction = null;

  // Fetch device mapping if needed for any tracker-specific data
  if (dataRequirements.needsDeviceMapping) {
    const defTrackers = await getAllDefaultTrackers(userId);
    const defaultDevices = defTrackers?.defaultDevices || [];
    defaultDevicePerTrackerId = defaultDevices.reduce((acc, dev) => { acc[dev.trackerId] = dev.deviceId; return acc;}, {});
  }

  // Fetch target achievements if needed
  let activeTargetIds = [];
  if (dataRequirements.needsTargetAchievements) {
    activeTargetIds = await getActiveTargetIds(userId);
    const lastTwoWeekStartDate = getAdjustedDate(endDate, -15);
    const allTargetAchievements = await getTargetsAchievedByDateRange(userId, lastTwoWeekStartDate, endDate, activeTargetIds, true);
    allTargetAchievements.forEach(achievement => {
      if(!formattedTargetAchievements[achievement.targetId]) formattedTargetAchievements[achievement.targetId] = [];
      formattedTargetAchievements[achievement.targetId].push(achievement);
    });
  }

  // Fetch CGM data if needed
  if (dataRequirements.needsCGMData) {
    const cgmStartDate = new Date(Date.now() - 864e5); // Last 24 hours
    CGMData = await getAllLogsByDateRange(userId, trackerMap[egvsTrackerId].indexName, cgmStartDate.toISOString(), new Date().toISOString(), true, defaultDevicePerTrackerId[egvsTrackerId] ? [defaultDevicePerTrackerId[egvsTrackerId]] : []);
  }

  // Fetch meal logs if needed
  if (dataRequirements.needsMealLogs) {
    const cgmStartDate = new Date(Date.now() - 864e5); // Last 24 hours
    mealLogsForDay = await getUserMealLogsByDate(userId, cgmStartDate, new Date());
  }

  // Fetch chat interaction if needed
  if (dataRequirements.needsChatData) {
    lastChatInteraction = await getLastChatInteraction(userId);
  }

  userData.data = {
    defaultDevicePerTrackerId,
    formattedTargetAchievements,
    targetIds: {
      sleep: sleepTargetId,
      meallog: mealLogTargetId,
    },
    activeTargetIds,
    dateTimeData: {    
      startDate, 
      endDate,
      activeWindowStartDate,
      activeWindowEndDate,
      prevActiveWindowStartDate,
      prevActiveWindowEndDate
    },
    CGMData,
    mealLogsForDay,
    lastChatInteraction,
    UTCOffsetMin
  };
  // Pass user's logs data like sleep, meallog, steps to the engine for evaluation
  const nudges = engine.evaluate(userData);
  logger.info(`Nudges received: ${JSON.stringify(nudges)}`);

  const nudgeResponse = {};
  for(const nudgeDoc of nudges){
    const docId = await upsertUserRecommendations(userId, nudgeDoc, nudgeDoc.type, nudgeDoc.ruleId);
    nudgeResponse[docId] = nudgeDoc;
  }
  return nudgeResponse;
}

async function getActiveTargetIds(userId) {
  const excludeTargetIds = [41, 42, 43, 44]; // These targetIds should be excluded as they are dummy
  const targetsMap = await getStaticTargetsMap();

  const targetIds = Object.keys(targetsMap)
    .filter(id => targetsMap[id].duration === 7 && !excludeTargetIds.includes(Number(id)))
    .map(Number);

  const latestTargetMap = await getLatestTargetsByIds(userId, targetIds);
  const latestTargetDetails = Object.values(latestTargetMap);
  const activeTargetIds = latestTargetDetails
    .filter(t => t.isActive)
    .map(t => Number(t.targetId));

  return activeTargetIds;
}
