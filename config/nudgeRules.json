[{"ruleId": "sleep_meal_gap", "enabled": true, "params": {"minGapHours": 3}, "prompt": "Try to finish your last meal at least {{minGapHours}} hours before your bedtime.", "priority": "high", "ttl": 86400, "requiredTrackerIds": [1, 5], "minUserAgeDays": 2}, {"ruleId": "bedtime_variability", "enabled": true, "params": {"shiftThresholdHrs": 2}, "prompt": "Your bedtime has varied by more than {{shiftThresholdHrs}} hours this week. A consistent sleep schedule helps improve recovery.", "priority": "medium", "ttl": 86400, "requiredTrackerIds": [5], "minUserAgeDays": 7}, {"ruleId": "late_meal_night", "enabled": true, "params": {"lateHour": 22, "daysThreshold": 2}, "prompt": "You've been logging late-night meals past {{lateHour}}:00 on {{daysThreshold}} or more days. Consider lighter meals earlier in the evening.", "priority": "medium", "ttl": 86400, "requiredTrackerIds": [1], "minUserAgeDays": 2}, {"ruleId": "missed_target_seven_day", "enabled": true, "params": {}, "prompt": "It looks like you've missed your daily habits for 7 days in a row. Would you like to update your habits or plans?", "priority": "high", "ttl": 172800, "requiredTrackerIds": "any", "minUserAgeDays": 7}, {"ruleId": "missed_target_three_day", "enabled": true, "params": {}, "prompt": "You've fallen short of one or more daily habits for 3 straight days. Let’s get back on track!", "priority": "low", "ttl": 86400, "requiredTrackerIds": "any", "minUserAgeDays": 3}, {"ruleId": "unattainable_goal_trend", "enabled": true, "params": {}, "prompt": "Based on your recent trends, your goal may not be reachable within the program timeframe. Let’s adjust our goals and win small.", "priority": "high", "ttl": 259200, "requiredTrackerIds": "any", "minUserAgeDays": 7}, {"ruleId": "weekly_target_drift", "enabled": true, "params": {"driftPercent": 50}, "prompt": "Your average {{tracker}} value has changed by over {{driftPercent}}% from last week. Let’s see if this aligns with your goals.", "priority": "medium", "ttl": 86400, "requiredTrackerIds": "any", "minUserAgeDays": 14}, {"ruleId": "cgm_spike_no_meal", "enabled": true, "params": {"windowMins": 60, "glucoseDeltaThreshold": 35, "aucThreshold": 4000, "peakGlucoseThreshold": 150, "recoveryTimeThreshold": 45}, "prompt": "We noticed a sustained glucose rise starting around {{time}}. Did you happen to have a snack or meal around that time? Logging it helps improve your personalized insights.", "priority": "medium", "ttl": 86400, "requiredTrackerIds": [8, 1], "minUserAgeDays": 0}, {"ruleId": "cgm_spike_threshold", "enabled": true, "params": {"threshold": 140}, "prompt": "Your glucose crossed {{threshold}} mg/dL. Let’s take a look at how recent meals or activity might be contributing.", "priority": "medium", "ttl": 86400, "requiredTrackerIds": [8], "minUserAgeDays": 0}, {"ruleId": "missing_tracker_logs", "enabled": true, "params": {"missingDays": 3}, "prompt": "We haven’t received data from one or more active trackers for {{missingDays}} days. Please check device sync or permissions.", "priority": "medium", "ttl": 86400, "requiredTrackerIds": "any", "minUserAgeDays": 3}, {"ruleId": "no_recent_chat", "enabled": true, "params": {"days": 7}, "prompt": "You haven't checked in with your wellness agent in over {{days}} days. Want to reconnect and get personalized guidance?", "priority": "low", "ttl": 172800, "requiredTrackerIds": "none", "minUserAgeDays": 7}]