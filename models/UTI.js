const mongoose = require("mongoose");
const { config } = require("../environment/index");
const COLLECTION_NAME = config.Collections.UTI;

const IngredientsSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      match: [/^[A-Za-z.,-\\\s'&_()\[\]]*$/, "Invalid ingredient name"],
      trim: true,
    },

    description: {
      type: String,
      trim: true,
    },

    ingredientId: {
      type: mongoose.Types.ObjectId,
      required: [true, "Ingredient Id is required"],
    },

    servingSize: {
      type: String,
      required: [true, "Serving Size is required"],
    },

    servingQty: {
      type: Number,
      requried: [true, "Serving Quantity is required"],
    },

    weightInGrams: {
      type: Number,
    },

    generalImage: {
      type: String,
      // default: url
    },
  },
  { _id: false }
);

const UTISchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: [true, "User id is requried"],
    },

    tagName: {
      type: String,
      trim: true,
      required: [true, "Tag is requried"],
      // ref: 'MealTagsModel'?
    },

    tagId: {
      type: mongoose.Types.ObjectId,
      required: [true, "Tag id is required"],
      // ref: 'MealTagsModel'?
    },

    lastUsed: {
      type: Date,
      required: true,
    },

    servingQty: {
      type: Number,
      requried: [true, "Serving Quantity is required"],
    },

    ingredients: {
      type: [IngredientsSchema],
      required: [true, "Ingredients are required"],
    },

    computedNutritions: {
      type: {}, // can use map: type: Map, of: Number..
      requried: true,
    },

    usedCount: {
      type: Number, 
      default: 0, 
    },

    imagePath: {
      type: String, 
      trim: true,
    },

    nutriScore: {
      type: Number,
    },
    
    grade: {
      type: String,
    },
  },
  { timestamps: true, versionKey: false, collection: COLLECTION_NAME }
);

// only one document for one tag by one user.
UTISchema.index({ userId: 1, tagId: 1 }, { unique: true });

module.exports.UTIModel = mongoose.model("UTI", UTISchema);
module.exports.IngredientsSchemaExport = IngredientsSchema;
