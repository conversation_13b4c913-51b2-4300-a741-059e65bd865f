const mongoose = require("mongoose");
const { IngredientsSchemaExport } = require("./UTI.js");
const { config } = require("../environment/index");
const COLLECTION_NAME = config.Collections.MEAL_LOGS;
const foodTypesArray = Object.keys(config.foodTypes);

const CoordinatesSchema = new mongoose.Schema(
  {
    x: { type: Number },
    y: { type: Number },
  },
  { _id: false }
);

const MealTagsSchema = new mongoose.Schema(
  {
    tagId: {
      type: mongoose.Types.ObjectId,
    },
    tagName: {
      type: String,
    },
    subImgId: {
      type: String,
    },
    imagePath: { type: String },
    coordinates: {
      type: [CoordinatesSchema],
    },
    ingredients: {
      type: [IngredientsSchemaExport],
    },
    servingQty: {
      type: Number,
    },
    computedNutritions: {
      type: {}, // can use map: type: Map, of: Number..
    },
    nutriScore: {
      type: Number,
    },
    grade: {
      type: String,
    },
  },
  { _id: false }
);

const MealLogsSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: [true, "User id is required"],
    },
    date: {
      type: Date,
      required: [true, "Date is required"],
    },
    type: {
      type: String,
      enum: ["breakfast", "dinner", "lunch", "snacks"],
      required: [true, "Meal type is required"],
    },
    jobId: {
      type: String,
    },
    imagePath: { type: String },
    mealTags: [MealTagsSchema],
    foodType: { 
      type: String,
      enum: foodTypesArray,
    },
    fruitVegPercentage: { type: Number },
    nutriScore: {
      type: Number,
    },
    grade: {
      type: String,
    },
    review: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true, versionKey: false, collection: COLLECTION_NAME }
);

MealLogsSchema.pre('validate', function (next) {
  if (!this.imagePath && !this.mealTags?.length)
      return next(new Error("At least one field(imagePath, mealTags) should be present"))
  next()
})

module.exports.MealLogsModel = mongoose.model("MealLog", MealLogsSchema);
