const { getOSClient } = require("../utils/connection");
const { getStaticTrackers }  = require("../utils/staticData")
const { config } = require("../environment/index");
const indexName = config.INDEX.trackers;

async function getAllDefaultTrackers(userId) {
  const client = await getOSClient();

  const response = await client.search({
    index: indexName,
    body: {
      query: { match: { "userId.keyword": userId } },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || {};
  return data;
}

async function getTrackerIdsByCategories(categories) {
  const trackerMap = await getStaticTrackers();
  return categories.flatMap(category =>
    (trackerMap.find(item => item.category === category)?.subCategories || []).map(sub => sub.id)
  );
}

module.exports = {
  getAllDefaultTrackers,
  getTrackerIdsByCategories,
}
