const { getOSClient } = require("../utils/connection");
const { config } = require("../environment/index");
const MANUAL_ENTRY_DEVICE_ID = -1;

const trackerMap = {
  1: {
    // meal log: all the operations like meallog CRUD & target computation will happen in meal log service
    indexName: null,
  },
  2: {
    indexName: config.INDEX.water,
  },
  3: {
    indexName: config.INDEX.activitySummary,
  },
  4: {
    indexName: config.INDEX.activity,
  },
  5: {
    indexName: config.INDEX.sleep,
  },
  6: {
    indexName: config.INDEX.bp,
  },
  7: {
    indexName: config.INDEX.bg,
  },
  8: {
    indexName: config.INDEX.egvs,
  },
  9: {
    indexName: config.INDEX.spo2,
  },
  10: {
    indexName: config.INDEX.heartRate,
  },
  11: {
    indexName: config.INDEX.hrv,
  },
  12: {
    indexName: config.INDEX.vo2,
  },
  13: {
    indexName: config.INDEX.ecg,
  },
  14: {
    indexName: config.INDEX.height,
  },
  15: {
    indexName: config.INDEX.weight,
  },
  16: {
    indexName: config.INDEX.fat,
  },
  17: {
    indexName: config.INDEX.bmi,
  },
  18: {
    indexName: config.INDEX.temp,
  },
  19: {
    indexName: config.INDEX.waistSize,
  },
  20: {
    indexName: config.INDEX.hipSize,
  },
  21: {
    indexName: config.INDEX.chestSize,
  },
  22: {
    indexName: config.INDEX.armSize,
  },
  23: {
    indexName: config.INDEX.quadSize,
  },
  24: {
    indexName: config.INDEX.mindfulness,
  },
  25: {
    indexName: config.INDEX.restingHeartRate,
  },
};

async function getAllLogsByDateRange(userId, indexName, startTime, endTime, filterByDeviceId, deviceIds = []) {
  const client = getOSClient();
  const query = {
    bool: {
      must: [
        { match: { "userId.keyword": userId } },
        { range: { timestamp: { gte: startTime, lte: endTime } } }
      ]
    }
  };
  if (filterByDeviceId) {
    query.bool.must.push({ terms: { deviceId: deviceIds } });
  }
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ "timestamp" : { order: "desc" } }],
      size: 2000,
      query,
    },
  });

  if (response.body?.hits) {
    const data = response.body.hits.hits.map((itm) => {
      return { id: itm._id, ...itm._source };
    });
    return data;
  }
  return [];
}

async function getLastLogByDeviceId(userId, indexName, deviceIds = [], startTime, endTime) {
  const client = getOSClient();
  const query = {
    bool: {
      must: [
        { match: { "userId.keyword": userId } },
        { terms: { "deviceId": deviceIds } }
      ]
    }
  }
  if(startTime && endTime) {
    query.bool.must.push({ range: { timestamp: { gte: startTime, lte: endTime } } });
  }
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ "timestamp": { order: "desc" } }],
      from: 0,
      size: 1,
      query
    },
  });

  if (response.body?.hits?.hits?.length) {
    const itm = response.body.hits.hits[0];
    return { id: itm._id, ...itm._source };
  }
  return null;
}

module.exports = {
  trackerMap,
  MANUAL_ENTRY_DEVICE_ID,
  getLastLogByDeviceId,
  getAllLogsByDateRange,
};
