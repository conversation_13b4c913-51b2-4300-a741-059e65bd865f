const { getOSClient } = require("../utils/connection");
const { config } = require("../environment/index");

const indexName = config.INDEX.userProfiles;

async function getUserProfile(userId) {
  const client = getOSClient();
  const response = await client.search({
    index: indexName,
    body: {query: {bool: {must: [{ match: { "userId.keyword": userId } }]}}, size: 1},
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

module.exports = {
  getUserProfile,
}
