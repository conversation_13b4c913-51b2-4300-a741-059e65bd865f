const { getOSClient } = require("../utils/connection");
const { config } = require("../environment/index");

const indexName = config.INDEX.targets

async function getLatestTargetsByIds(userId, targetIds) {
  const client = getOSClient();

  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ createdAt: { order: "desc" } }],
      query: {
        bool: {
          must: [
            { terms: { targetId: targetIds.map(Number) } },
            { term: { "userId.keyword": userId } }
          ]
        }
      },
      size: 1000 // ensure enough results to cover all targetIds
    }
  });

  const hits = response.body?.hits?.hits || [];

  // Deduplicate: pick latest (first match) per targetId
  const latestByTargetsIds = {};
  for (const hit of hits) {
    const doc = hit._source;
    if (!latestByTargetsIds[doc.targetId]) {
      latestByTargetsIds[doc.targetId] = doc;
    }
  }

  return latestByTargetsIds;
}

module.exports = {
  getLatestTargetsByIds,
}
