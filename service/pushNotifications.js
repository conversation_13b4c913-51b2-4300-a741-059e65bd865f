const { sendPushNotification } = require("../utils/aws");
const { log } = require("../utils/logger");

const notificationEventNames = {
  sleep_meal_gap: "sleep_meal_gap",
  bedtime_variability: "bedtime_variability",
  late_meal_night: "late_meal_night",
  missed_target_seven_day: "missed_target_seven_day",
  missed_target_three_day: "missed_target_three_day",
  unattainable_goal_trend: "unattainable_goal_trend",
  weekly_target_drift: "weekly_target_drift",
  cgm_spike_no_meal: "cgm_spike_no_meal",
  cgm_spike_threshold: "cgm_spike_threshold",
  missing_tracker_logs: "missing_tracker_logs",
  no_recent_chat: "no_recent_chat",
};

const notificationDescription = {
  sleep_meal_gap: {
    type: "nudge",
    title: "Sleep & Meal Timing",
    alert: "Try to finish your last meal at least 3 hours before bedtime.",
  },
  bedtime_variability: {
    type: "nudge",
    title: "Sleep Schedule",
    alert:
      "Your bedtime varied a lot this week. Try to stay consistent for better recovery.",
  },
  late_meal_night: {
    type: "nudge",
    title: "Late-Night Meals",
    alert: "You've been eating late. Try lighter meals earlier in the evening.",
  },
  missed_target_seven_day: {
    type: "nudge",
    title: "7-Day Target Miss",
    alert:
      "You've missed daily habits for 7 days in a row. Time to revisit your plan?",
  },
  missed_target_three_day: {
    type: "nudge",
    title: "3-Day Target Miss",
    alert: "You’ve missed habits for 3 days straight. Let’s get back on track!",
  },
  unattainable_goal_trend: {
    type: "nudge",
    title: "Goal Adjustment",
    alert:
      "Your current trend suggests the goal may be too tough. Let’s review it.",
  },
  weekly_target_drift: {
    type: "nudge",
    title: "Weekly Trend Shift",
    alert:
      "Your tracker trend changed significantly this week. Check alignment with your goals.",
  },
  cgm_spike_no_meal: {
    type: "nudge",
    title: "Unexplained Glucose Spike",
    alert:
      "Glucose spiked but no meal was logged. Please ensure meal logs are up to date.",
  },
  cgm_spike_threshold: {
    type: "nudge",
    title: "High Glucose Alert",
    alert:
      "Your glucose crossed the defined threshold. Review your recent meals or activity.",
  },
  missing_tracker_logs: {
    type: "nudge",
    title: "Missing Tracker Data",
    alert:
      "No data received from one or more devices for a few days. Please check sync.",
  },
  no_recent_chat: {
    type: "nudge",
    title: "No Recent Check-In",
    alert: "You haven’t chatted with your coach in a while. Want to reconnect?",
  },
};

async function sendNotificationToUser(senderUserId, userId, notificationName, customData) {
  try {
    const { alert, title, type } = notificationDescription[notificationName];
    if (!alert || !title || !type) {
      return false;
    }
    const flag = await sendPushNotification(senderUserId, userId, alert, title, type, customData);
    return flag;
  } catch (error) {
    log.warn(`Error calling sendNotificationToUser, ${JSON.stringify(error)}`);
    return false;
  }
}

async function sendNotificationByNudgeRuleIds(userId, ruleIds) {
  const senderUserId = userId;
  const results = {};
  await Promise.all(
    ruleIds.map(async (ruleId) => {
      try {
        const success = await sendNotificationToUser(senderUserId, userId, ruleId, {});
        results[ruleId] = success;
      } catch (err) {
        log.warn(`Failed to send nudge ${ruleId}: ${err}`);
        results[ruleId] = false;
      }
    })
  );

  return results; // e.g. { sleep_meal_gap: true, missed_target_three_day: false }
}

module.exports = {
  notificationEventNames,
  notificationDescription,
  sendNotificationByNudgeRuleIds,
};
