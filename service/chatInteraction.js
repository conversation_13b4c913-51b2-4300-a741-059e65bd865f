const { getOSClient } = require("../utils/connection");
const { config } = require("../environment/index");

const indexName = config.INDEX.chatInteractions;

/**
 * Get the last chat interaction for a user
 * @param {string} userId - The user ID
 * @returns {Object|null} - The last chat interaction or null if none found
 */
async function getLastChatInteraction(userId) {
  const client = getOSClient();
  
  try {
    const response = await client.search({
      index: indexName,
      body: {
        _source: { excludes: ["userId"] },
        sort: [{ "timestamp": { order: "desc" } }],
        query: {
          bool: {
            must: [{ match: { "userId.keyword": userId } }]
          }
        },
        size: 1,
      },
    });

    if (response.body?.hits?.hits?.length) {
      const hit = response.body.hits.hits[0];
      return { id: hit._id, ...hit._source };
    }
    return null;
  } catch (error) {
    // If index doesn't exist or other error, return null
    // This allows the system to work even if chat interactions aren't tracked yet
    return null;
  }
}

module.exports = {
  getLastChatInteraction,
};
