# Environment Configuration

This directory contains the environment configuration system for the Target Achievement service.

## Overview

The environment configuration system uses:
- `.env` files for local development
- Environment variables for deployment
- AWS Secrets Manager for secure storage of sensitive values

## Directory Structure

- `environment/index.js` - Main configuration loader
- `environment/development.js` - Development environment configuration
- `environment/sandbox.js` - Sandbox environment configuration
- `environment/production.js` - Production environment configuration
- `environment/env/` - Directory containing environment-specific .env files
  - `.env.development` - Development environment variables
  - `.env.sandbox` - Sandbox environment variables
  - `.env.production` - Production environment variables
  - `.env.example` - Example template for environment variables

## Usage

### Local Development

1. Copy the example environment file:
   ```
   cp environment/env/.env.example environment/env/.env.development
   ```

2. Edit the `.env.development` file with your local configuration values.

3. Run the application with the development environment:
   ```
   npm run start:dev
   ```

### Running in Different Environments

Use the appropriate npm script to run in different environments:

- Development: `npm run start:dev`
- Sandbox: `npm run start:sandbox`
- Production: `npm run start:prod`

### Deploying to AWS

1. Ensure your environment variables are properly set in the corresponding `.env` file.

2. Use the upload script to upload the environment variables to AWS Secrets Manager:
   ```
   ./scripts/upload-env-to-aws.sh --env [development|sandbox|production]
   ```

3. The Lambda function will retrieve these environment variables from AWS Secrets Manager at runtime.

## Environment Variables

The following environment variables are used:

| Variable | Description | Example |
|----------|-------------|---------|
| OS_HOST | OpenSearch host URL | https://search-example.region.es.amazonaws.com |
| REGION | AWS region | us-east-1 |
| AWS_TRACKERS_STATIC_DATA_LAMBDA | Name of the trackers static data Lambda function | trackers-static-dev |
| AWS_SQS_URL | URL of the SQS queue | https://sqs.region.amazonaws.com/account-id/queue-name.fifo |

## AWS Profiles

When running commands locally or using the upload script, different AWS profiles are used for different environments:

- Development: `development` profile
- Sandbox: `sandbox` profile
- Production: `production` profile

These profiles are only used for local development and the upload script, not in the deployed Lambda function. In AWS Lambda, the execution role provides the necessary permissions.

To configure AWS profiles for local development, use the AWS CLI:

```
aws configure --profile development
aws configure --profile sandbox
aws configure --profile production
```

When using the upload script, you can specify the profile to use:

```
./scripts/upload-env-to-aws.sh --env development --profile development
```

## Security Considerations

- Never commit `.env` files to version control (they are added to `.gitignore`)
- Use AWS Secrets Manager for storing sensitive values in production
- Restrict access to AWS Secrets Manager based on IAM roles
- Regularly rotate credentials and update the secrets
